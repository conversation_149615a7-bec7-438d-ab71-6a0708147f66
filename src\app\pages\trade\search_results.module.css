@font-face {
    font-family: 'Poppins';
    font-weight: 300;
    src: url('../../../../public/fonts/Poppins-Light.ttf') format('truetype');
}

/* Header Content Styles */
.headerContent {
    margin-bottom: 32px;
    position: relative;
    z-index: 2;
}

.pageTitle {
    font-size: 28px;
    font-weight: 700;
    background: linear-gradient(135deg, #2563EB 0%, #1D4ED8 50%, #3B82F6 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin: 0 0 8px 0;
    letter-spacing: -0.8px;
    text-shadow: 0 2px 4px rgba(37, 99, 235, 0.1);
}

.pageSubtitle {
    font-size: 15px;
    color: #64748B;
    margin: 0;
    font-weight: 500;
    letter-spacing: 0.2px;
}

.main {
    background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 50%, #E2E8F0 100%);
    min-height: 100vh;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, sans-serif;
    padding: 20px 0;

    @media screen and (max-width : 576px) {
        width: 100%;
        padding: 10px;
    }
}

.rightContainerBody {
    height: 100%;
    padding: 40px;
    border-radius: 24px;
    background: linear-gradient(145deg, #FFFFFF 0%, #F8FAFC 100%);
    box-shadow:
        0 20px 25px -5px rgba(0, 0, 0, 0.1),
        0 10px 10px -5px rgba(0, 0, 0, 0.04),
        0 0 0 1px rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);

    @media screen and (max-width : 576px) {
        padding: 20px;
        border-radius: 16px;
    }
}

.body {
    height: 100%;
    background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);
    width: 100%;
}

.wrapper {
    display: flex;
    width: 100%;
    min-height: 90vh;
    padding: 40px 20px;
    margin: auto;
    justify-content: space-between;
    max-width: 1400px;

    @media screen and (max-width : 576px) {
        width: 100%;
        padding: 20px 10px;
        flex-direction: column;
    }
}

.leftContainerWrapper {
    width: 20%;
    position: relative;

    @media screen and (max-width : 576px) {
        display: none;
    }
}

.leftContainer {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    border-radius: 10px;

}

/* need to remove later */

.logoArea {
    height: 20%;
    width: 100%;
    background-color: #4153ed;
    border-radius: 15px 15px 15px 0px;
    display: flex;
    flex-direction: column;
}

.logo {
    margin-top: 5px;
}

.profileBar {
    margin-top: 25px;
}

.profileBarContainer {
    background-color: #4f535a;
    width: 80%;
    height: 35px;
    margin: auto;
    border-radius: 100px;
    background: rgba(255, 255, 255, 0.17);
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.profileImg {
    margin-top: 4px;
    margin-left: 15px;
    margin-right: 5px;
}

.profileName {
    color: #fff;
    font-family: Poppins;
    font-size: 11px;
    font-weight: 500;
}

.profileDropDown {
    margin-left: auto;
    margin-right: 10px;
}


.header {
    color: #000;
    font-family: Poppins;
    font-size: 24px;
    font-style: normal;
    font-weight: 500;

}

.logoContainer {
    /* width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: end; */
}

.logo {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.profileBar {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50%;
}



.rightContainer {
    width: 100%;
    height: auto;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    min-height: 100vh;

    @media screen and (max-width : 576px) {
        width: 100%;
        margin: auto;
    }
}

.rightContainerWrapper {
    width: 100%;
    padding: 32px;
    box-sizing: border-box;
    background: linear-gradient(145deg, #FFFFFF 0%, #F8FAFC 100%);
    border-radius: 24px;
    box-shadow:
        0 25px 50px -12px rgba(0, 0, 0, 0.08),
        0 0 0 1px rgba(255, 255, 255, 0.1);

    @media screen and (max-width: 576px) {
        padding: 20px;
        border-radius: 16px;
    }
}

.topBoxWrapper {
    width: 100%;
    border-bottom: 2px solid transparent;
    background: linear-gradient(90deg, rgba(37, 99, 235, 0.1) 0%, rgba(59, 130, 246, 0.05) 100%);
    border-image: linear-gradient(90deg, #2563EB, #3B82F6) 1;
    display: flex;
    justify-content: space-between;
    margin-bottom: 40px;
    margin-top: 0;
    padding: 24px;
    border-radius: 16px;
    backdrop-filter: blur(10px);

    @media screen and (max-width : 576px) {
        flex-direction: column;
        margin-bottom: 24px;
        padding: 20px;
        border-radius: 12px;
    }
}

.topLeftBox {
    width: 50%;
    margin-right: auto;
    align-items: center;

    @media screen and (max-width : 576px) {
        width: 100%;
        margin: 0;
    }
}

.headerBtn {
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    font-size: 18px;
    font-weight: 600;
    color: #1E293B;
    letter-spacing: 0.3px;
}

.HeaderBuyBtn {
    padding: 12px 20px;
    align-items: center;
    border-radius: 12px;
    border: none;
    background: linear-gradient(135deg, #10B981 0%, #059669 100%);
    color: #FFFFFF;
    font-weight: 700;
    font-size: 14px;
    margin-right: 12px;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    letter-spacing: 0.5px;
    text-transform: uppercase;
}

.timeSubHeader {
    display: flex;
    align-items: center;
    color: #475569;
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.5;
    letter-spacing: 0.2px;

    @media screen and (max-width : 576px) {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
}

.topRightBox {
    margin-left: auto;
    justify-content: space-evenly;
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.rightheaderinfo {
    display: flex;
    gap: 20px;
    color: #475569;
    font-family: 'Poppins', sans-serif;
    font-size: 13px;
    font-weight: 500;
    margin-bottom: 8px;

    @media screen and (max-width : 576px) {
        flex-direction: column;
        gap: 8px;
        margin-top: 16px;
    }
}

.rinfo {
    padding: 8px 16px;
    background: linear-gradient(135deg, #F1F5F9 0%, #E2E8F0 100%);
    border-radius: 8px;
    border: 1px solid rgba(148, 163, 184, 0.2);
    font-weight: 600;
    color: #334155;
    white-space: nowrap;
}

.rightheaderinfo1 {
    color: #2563EB;
    font-family: 'Poppins', sans-serif;
    font-size: 11px;
    font-weight: 500;
    margin-left: 0;
    border-radius: 8px;
    background: linear-gradient(135deg, #DBEAFE 0%, #BFDBFE 100%);
    display: inline-flex;
    padding: 8px 16px;
    justify-content: center;
    align-items: center;
    gap: 8px;
    border: 1px solid rgba(37, 99, 235, 0.2);
}

.bottomBoxWrapper {
    display: flex;
    width: 100%;
    justify-content: space-between;
    gap: 32px;

    @media screen and (max-width : 576px) {
        flex-direction: column;
        gap: 20px;
    }
}

.bottomLeftBox {
    width: 68%;
    background: linear-gradient(145deg, #FFFFFF 0%, #F8FAFC 100%);
    padding: 32px;
    border-radius: 20px;
    box-sizing: border-box;
    box-shadow:
        0 10px 25px -5px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(148, 163, 184, 0.1);

    @media screen and (max-width : 576px) {
        width: 100%;
        padding: 20px;
        border-radius: 16px;
    }
}


.progressBarArea {
    margin-bottom: 32px;
    display: flex;
    width: 100%;
    flex-wrap: wrap;
    padding: 20px;
    background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);
    border-radius: 16px;
    border: 1px solid rgba(148, 163, 184, 0.1);
}

.confirmOrderInfoArea {
    margin-bottom: 32px;
}

.orderConfirmHeader {
    color: #1E293B;
    font-family: 'Poppins', sans-serif;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    letter-spacing: 0.3px;
}

.orderConfirmInfo {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 16px;
    gap: 8px;
    flex-wrap: wrap;

    @media screen and (max-width : 576px) {
        flex-direction: column;
        gap: 12px;
    }
}

.orderDialogue {
    color: #475569;
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px;
    background: linear-gradient(135deg, #FEF3C7 0%, #FDE68A 100%);
    border-radius: 12px;
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.orderConfirmInfoSingle {
    margin-right: 0;
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
    font-weight: 600;
    color: #1E293B;
    padding: 8px 16px;
    background: linear-gradient(135deg, #F1F5F9 0%, #E2E8F0 100%);
    border-radius: 8px;
    border: 1px solid rgba(148, 163, 184, 0.2);
    white-space: nowrap;
}


.paymentInfoContainer {
    width: 100%;
    min-height: 180px;
    display: flex;
    margin-bottom: 24px;
    gap: 16px;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

    @media screen and (max-width : 576px) {
        flex-direction: column;
        gap: 12px;
    }
}

.paymentMethodDisplay {
    width: 35%;
    height: 100%;
    background: linear-gradient(135deg, #DBEAFE 0%, #BFDBFE 100%);
    border: 1px solid rgba(37, 99, 235, 0.2);
    padding: 20px;
    border-radius: 16px;
    display: flex;
    flex-direction: column;
    gap: 12px;

    @media screen and (max-width : 576px) {
        width: 100%;
    }
}

.paymentMethod {
    display: flex;
    justify-content: center;
    align-items: center;
    height: auto;
    padding: 16px 20px;
    background: linear-gradient(135deg, #2563EB 0%, #1D4ED8 100%);
    color: #FFFFFF;
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
    font-weight: 700;
    border-radius: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.paymentAddressDisplay {
    width: 65%;
    min-height: 180px;
    border: 1px solid rgba(148, 163, 184, 0.2);
    background: linear-gradient(145deg, #FFFFFF 0%, #F8FAFC 100%);
    border-radius: 16px;
    overflow: hidden;

    @media screen and (max-width : 576px) {
        width: 100%;
    }
}

.paymentAddressName {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    color: #334155;
    font-family: 'Poppins', sans-serif;
    font-size: 13px;
    font-weight: 600;
    border-bottom: 1px solid rgba(148, 163, 184, 0.1);
    background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);

    &:last-child {
        border-bottom: none;
    }
}

.uploadDialogue {
    color: #2563EB;
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px;
    background: linear-gradient(135deg, #DBEAFE 0%, #BFDBFE 100%);
    border-radius: 12px;
    border: 1px solid rgba(37, 99, 235, 0.2);
    margin-bottom: 24px;
}

.ctaDialogue {
    color: #475569;
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
    font-weight: 500;
}

.activityBtnArea {
    height: auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
    margin-top: 32px;

    @media screen and (max-width : 576px) {
        flex-direction: column;
        gap: 12px;
        margin-bottom: 80px;
    }
}

.leftBtns {
    display: flex;
    gap: 12px;
    align-items: center;

    @media screen and (max-width : 576px) {
        width: 100%;
        justify-content: center;
    }
}

.notifySellerBtn {
    display: inline-flex;
    height: 48px;
    padding: 12px 24px;
    justify-content: center;
    align-items: center;
    gap: 8px;
    border-radius: 12px;
    background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
    font-weight: 700;
    color: #FFFFFF;
    cursor: pointer;
    border: none;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
    transition: all 0.2s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(245, 158, 11, 0.4);
    }
}

.cancelBtn {
    display: inline-flex;
    height: 48px;
    padding: 12px 24px;
    justify-content: center;
    align-items: center;
    gap: 8px;
    border-radius: 12px;
    background: linear-gradient(135deg, #F1F5F9 0%, #E2E8F0 100%);
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
    font-weight: 600;
    color: #64748B;
    cursor: pointer;
    border: 1px solid rgba(148, 163, 184, 0.2);
    transition: all 0.2s ease;

    &:hover {
        background: linear-gradient(135deg, #E2E8F0 0%, #CBD5E1 100%);
        color: #475569;
    }
}

.reportBtn {
    display: inline-flex;
    height: 48px;
    padding: 12px 24px;
    justify-content: center;
    align-items: center;
    border-radius: 12px;
    background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
    color: #FFFFFF;
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
    font-weight: 700;
    cursor: pointer;
    border: none;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
    transition: all 0.2s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(239, 68, 68, 0.4);
    }
}

.bottomRightBox {
    height: fit-content;
    width: 32%;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    border-radius: 20px;
    box-sizing: border-box;
    background: linear-gradient(145deg, #FFFFFF 0%, #F8FAFC 100%);
    box-shadow:
        0 10px 25px -5px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(148, 163, 184, 0.1);
    overflow: hidden;

    @media screen and (max-width : 576px) {
        width: 100%;
        margin-bottom: 30px;
        border-radius: 16px;
    }
}


/* modal */
/* .modalWrapper {
    width: 700px;
} */

.modalHeaderCont {
    display: flex;
    justify-content: center;
    align-items: center;
}

.modalHeader {
    color: #2563EB;
    font-family: 'Poppins', sans-serif;
    font-size: 24px;
    font-weight: 700;
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    letter-spacing: 0.3px;
}

.modalHeader2 {
    color: #2563EB;
    font-family: 'Poppins', sans-serif;
    font-size: 24px;
    font-weight: 700;
    display: flex;
    width: 100%;
    justify-content: center;
    margin-bottom: 20px;
    letter-spacing: 0.3px;
}

.issueSelect {
    color: #1E293B;
    font-family: 'Poppins', sans-serif;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    display: flex;
}

.issueSelect2 {
    color: #1E293B;
    font-family: 'Poppins', sans-serif;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    justify-content: center;
    display: flex;
}

.optionsBox {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 20px;
    background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);
    border-radius: 16px;
    border: 1px solid rgba(148, 163, 184, 0.1);
}

.inputBoxes {
    margin: 8px 0;
}

.options {
    color: #1E293B;
    font-family: 'Poppins', sans-serif;
    font-size: 16px;
    font-weight: 600;
    margin: 16px 0;
    letter-spacing: 0.3px;
    padding: 12px 16px;
    background: linear-gradient(135deg, #FFFFFF 0%, #F8FAFC 100%);
    border-radius: 12px;
    border: 1px solid rgba(148, 163, 184, 0.2);
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
        background: linear-gradient(135deg, #F1F5F9 0%, #E2E8F0 100%);
        border-color: #2563EB;
    }
}

.inputTextArea input {
    display: flex;
    width: 100%;
    height: 80px;
    padding: 16px 20px;
    border-radius: 12px;
    border: 1px solid rgba(148, 163, 184, 0.2);
    background: linear-gradient(135deg, #FFFFFF 0%, #F8FAFC 100%);
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
    color: #334155;
    resize: vertical;
    transition: all 0.2s ease;

    &:focus {
        outline: none;
        border-color: #2563EB;
        box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    }
}

.submitBtnWrapper {
    width: 100%;
    border-radius: 12px;
    background: linear-gradient(135deg, #2563EB 0%, #1D4ED8 100%);
    height: 48px;
    margin-top: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
    transition: all 0.2s ease;

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(37, 99, 235, 0.4);
    }
}

.submitBtnWrapper2 {
    width: 100%;
    border-radius: 12px;
    height: 48px;
    margin-top: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12px;
}

.submitBtn {
    color: #FFFFFF;
    text-align: center;
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
    font-weight: 700;
    background-color: transparent;
    outline: none;
    border: none;
    cursor: pointer;
    letter-spacing: 0.3px;
}

.disputeClose {
    height: 48px;
    padding: 12px 24px;
    color: #64748B;
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
    font-weight: 600;
    border-radius: 12px;
    background: linear-gradient(135deg, #F1F5F9 0%, #E2E8F0 100%);
    border: 1px solid rgba(148, 163, 184, 0.2);
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
        background: linear-gradient(135deg, #E2E8F0 0%, #CBD5E1 100%);
    }
}

.disputeSubmit {
    height: 48px;
    padding: 12px 24px;
    border-radius: 12px;
    background: linear-gradient(135deg, #2563EB 0%, #1D4ED8 100%);
    color: #FFFFFF;
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
    font-weight: 700;
    border: none;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
    transition: all 0.2s ease;

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(37, 99, 235, 0.4);
    }
}

.finalSubmitBtn {
    width: 100%;
    height: 48px;
    border-radius: 12px;
    border: none;
    background: linear-gradient(135deg, #2563EB 0%, #1D4ED8 100%);
    color: #FFFFFF;
    text-align: center;
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
    font-weight: 700;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
    transition: all 0.2s ease;
    letter-spacing: 0.3px;

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(37, 99, 235, 0.4);
    }
}

.inputUpload {
    width: 100%;
    height: 48px;
    border-radius: 12px;
    background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);
    border: 1px solid rgba(148, 163, 184, 0.2);
    padding: 12px 16px;
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
    color: #334155;
    transition: all 0.2s ease;

    &:focus {
        outline: none;
        border-color: #2563EB;
        box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    }
}

.passedTerms {
    margin-top: 8px;
    font-size: 13px;
    font-family: 'Poppins', sans-serif;
    font-weight: 500;
    min-height: 150px;
    color: #475569;
    line-height: 1.6;
    padding: 16px;
    background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);
    border-radius: 12px;
    border: 1px solid rgba(148, 163, 184, 0.1);
}

.tradePayDetails {
    display: flex;
    list-style-type: none;
    justify-content: flex-start;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.payLi {
    margin: 0;
    padding: 8px 12px;
    background: linear-gradient(135deg, #F1F5F9 0%, #E2E8F0 100%);
    border-radius: 8px;
    border: 1px solid rgba(148, 163, 184, 0.2);
    font-size: 12px;
    font-weight: 600;
    color: #334155;
}

.payoutDetailsCont {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
}


.tagArea {
    width: 100%;
    margin-bottom: 32px;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: center;
    gap: 16px;
}

.senderTag {
    display: flex;
    width: 100%;
    justify-content: center;
    align-items: center;
    background: linear-gradient(135deg, #10B981 0%, #059669 100%);
    padding: 16px 24px;
    border-radius: 12px;
    font-weight: 700;
    font-family: 'Poppins', sans-serif;
    font-size: 15px;
    color: #FFFFFF;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.peerTag {
    display: flex;
    width: 100%;
    justify-content: center;
    align-items: center;
    background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
    padding: 16px 24px;
    border-radius: 12px;
    font-weight: 700;
    font-family: 'Poppins', sans-serif;
    font-size: 15px;
    color: #FFFFFF;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.dialogueSteps {
    margin-left: 12px;
    background: linear-gradient(135deg, #6366F1 0%, #4F46E5 100%);
    color: #FFFFFF;
    padding: 12px 20px;
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    border-radius: 10px;
    font-size: 13px;
    letter-spacing: 0.3px;
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
    border: 1px solid rgba(99, 102, 241, 0.2);
}