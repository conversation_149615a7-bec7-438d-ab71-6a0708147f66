@font-face {
  font-family: 'Poppins';
  font-weight: 300;
  src: url('../../../../public/fonts/Poppins-Light.ttf') format('truetype');
}

.mainContainer {
  width: 100%;
  height: auto;
  display: flex;


  @media screen and (max-width: 576px) {
    flex-direction: column;
  }

  @media screen and (min-width: 1500px) {
    height: 100vh;
  }

}

.topContainer {
  @media screen and (max-width: 576px) {
    width: 100%;
    display: block;
    position: relative;
  }

  @media screen and (min-width: 577px) {
    display: none;
  }
}



.leftContainer {
  width: 60%;
  display: flex;

  @media screen and (max-width: 576px) {
    width: 100%;
  }
}

.leftWrapper {
  padding: 80px;
  width: 100%;
  display: flex;
  flex-direction: column;

  @media screen and (max-width: 577px) {
    padding: 0px;
  }
}

.silverWrapper {
  padding: 30px;
  width: 80%;
  margin-left: auto;
  margin-right: auto;
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  border: 1px solid black;
  position: relative;
  border-radius: 5px;
  margin-bottom: 20px;

  @media screen and (max-width: 577px) {
    width: 80%;
  }
}

.goldWrapper {
  padding: 30px;
  width: 80%;
  margin: auto;
  display: flex;
  flex-direction: column;
  border: 1px solid gold;
  position: relative;
  border-radius: 5px;
}

.silverTag {
  width: 200px;
  height: 30px;
  background-color: silver;
  position: absolute;
  top: -15px;
  text-align: center;
  font-weight: 600;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 5px;
}

.goldTag {
  width: 200px;
  height: 30px;
  background-color: gold;
  position: absolute;
  top: -15px;
  text-align: center;
  font-weight: 600;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 5px;
}

.backBtn {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  height: 50px;
  margin-bottom: 20px;
  font-size: 14px;

  @media screen and (max-width: 576px) {
    position: absolute;
    top: 10px;
    left: 14px;
  }
}

.backimg {
  margin-right: 10px;
  cursor: pointer;

  @media screen and (max-width: 576px) {
    margin-right: 0px;
  }
}

.mainButtonsWrapper {
  display: flex;
  width: 100%;
  margin-bottom: 25px;
  font-family: Poppins;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
}

.personalBtn {
  color: #4153ed;
  border: 1px solid #4153ed;
  border-radius: 5px;
  padding: 8px 10px 8px 10px;
  margin-right: 8px;
  font-size: 14px;
}

.employBtn {
  color: #000;
  border: 1px solid black;
  border-radius: 5px;
  padding: 8px 10px 8px 10px;
}

.heading {
  color: #4153ed;
  font-family: Poppins;
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  margin-bottom: 30px;
}

.formWrapper {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;

  @media screen and (max-width : 576px) {
    flex-direction: column;
  }
}

.firstName {
  width: 92%;

  @media screen and (max-width : 576px) {
    width: 100%;
  }
}

.firstNameOther {
  width: 96%;


  @media screen and (max-width : 576px) {
    width: 100%;
  }
}

.firstNameLabel {
  font-size: 12px;
  font-family: poppins;
  margin-top: 10px;
  /* text-align: center; */

  @media screen and (max-width : 576px) {
    margin-bottom: 5px;
  }
}

.addressName {
  width: 100%;
}

.lastName {
  width: 92%;
}

.firstNameInput input {
  border: none;
  background-color: #f9f9f9;
  width: 100%;
  padding-left: 10px;
  padding-right: 10px;

  @media screen and (max-width : 576px) {
    margin-bottom: 5px;
  }
}

.firstNameInputKYC input {
  border: none;
  background-color: #f9f9f9;
  width: 100%;
  padding-left: 10px;
  padding-right: 10px;

  @media screen and (max-width : 576px) {
    margin-bottom: 5px;
  }
}

.firstNameInput select {
  border: none;
  background-color: #f9f9f9;
  width: 100%;
  padding-left: 10px;
  padding-right: 10px;
}

.lastNameInput input {
  border: none;
}

.firstNameInputKYC select {
  border: none;
  background-color: #f9f9f9;
  width: 100%;
  padding-left: 10px;
  padding-right: 10px;
}

.lastNameInput input {
  border: none;
}

.firstNameInput {
  width: 92%;
  height: 40px;
  border-radius: 2px;
  background: #f9f9f9;
  display: flex;
  font-family: poppins;

  @media screen and (max-width : 576px) {
    width: 100%;
  }
}

.firstNameInputOther {
  width: 100%;
  height: 40px;
  border-radius: 2px;

  display: flex;
  background: #f9f9f9;
  outline: none;
  border: none;
  width: 100%;
  padding-left: 5px;

  @media screen and (max-width : 576px) {
    width: 100%;
  }
}

.firstNameInputKYC {
  width: 100%;
  height: 40px;
  border-radius: 2px;
  background: #f9f9f9;
  display: flex;

  @media screen and (max-width : 576px) {
    width: 100%;
  }
}

.kycSubmitBtn {
  display: flex;
  align-items: center;
  justify-content: center;
}

.kycSubmitBtn button {
  color: #fff;
  background-color: #4153ed;
  height: 40px;
  padding: 10px 50px;
  border-radius: 5px;
  outline: none;
  border: 1px solid #4153ed;
  cursor: pointer;

}

.lastNameInput {
  width: 92%;
  border: 1px solid black;
  height: 40px;
  border-radius: 2px;
  background: #f9f9f9;
}

.emailBtn {
  border-radius: 0px 2px 2px 0px;
  background: #f5f5f5;
  border: 1px solid #ebebeb;
  border-left: 2px solid #c4c3c3;
  font-size: 8px;
}

.calender {
  display: flex;
  justify-content: center;
  align-items: center;
}

.addressNameInput input {
  cursor: pointer;
  background-color: #f9f9f9;
  width: 96%;
  height: 40px;
  padding-left: 10px;
  padding-right: 10px;
  position: relative;
}

.addressNameInput {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #c4c3c3;

}





.fileInput[type='file'] {
  opacity: 1
}



.upload {
  position: absolute;
  right: 30px;
  top: 12px;
}

/* submitBtn */

.submitBtnCont {
  display: flex;
  justify-content: center;
  align-items: center;
}

.submitBtnCont1 {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 10px;
}

.submitBtn {
  background-color: #fff;
  padding: 15px 50px;
  color: #4153ed;
  gap: 10px;
  font-weight: 600;
  border-color: #4153ed;
  outline: none;
  border-radius: 3px;
  cursor: pointer;
}

.nextBtnCont {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 30px;
}

.nextsubmitBtn {
  background-color: #4153ed;
  padding: 15px 120px;
  color: white;
  gap: 10px;
  font-weight: 600;
  border-color: #4153ed;
  outline: none;
  border-radius: 3px;
  margin-bottom: 30px;
  cursor: pointer;
}

/* submitBtn */


.rightContainer {
  width: 40%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #F8F9FB;

  @media screen and (max-width: 576px) {
    display: none;
  }
}

.firstNameInputKYC input {
  border: none;
  background-color: #f9f9f9;
  width: 100%;
  padding-left: 10px;
  padding-right: 10px;

  @media screen and (max-width : 576px) {
    margin-bottom: 5px;
  }
}

.firstNameInputKYC select {
  border: none;
  background-color: #f9f9f9;
  width: 100%;
  padding-left: 10px;
  padding-right: 10px;
}

.lastNameInput input {
  border: none;
}

.firstNameInputKYC {
  width: 100%;
  height: 40px;
  border-radius: 2px;
  background: #f9f9f9;
  display: flex;

  @media screen and (max-width : 576px) {
    width: 100%;
  }
}

.kycSubmitBtn {
  display: flex;
  align-items: center;
  justify-content: center;
}

.kycSubmitBtn button {
  color: #fff;
  background-color: #4153ed;
  height: 40px;
  padding: 10px 50px;
  border-radius: 5px;
  outline: none;
  border: 1px solid #4153ed;


}

.submitBtnCont1 {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 10px;
}

.submitBtn1 {
  background-color: #fff;
  padding: 15px 50px;
  color: #212124;
  gap: 10px;
  font-weight: 600;
  border-color: #212124;
  outline: none;
  border-radius: 3px;
  cursor: pointer;
}

.manualKycContainer {
  width: 70%;
  margin: 20px auto 10px auto;
}

.kycUpload {
  margin-top: 20px;
}

.others {
  width: 100%;
}